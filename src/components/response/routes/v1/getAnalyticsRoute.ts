import { Router } from 'express';
import {
  ExtractOriginFromRequest,
  BlockRequestByOrigin,
  ExtractIPAddressFromOrigin,
  ExtractCountryFromIPAddress,
  BlockLoggedOutAccount,
  ExtractAccountIdFromRequest,
  BlockNonExistentAccountById,
  ValidateSurveyOwnership,
} from '../../../../global/middlewares';
import { validateGetSurveyPayload, formatGetSurveyPayload } from '../../../survey/middlewares';
import { GenerateApiVersionPath } from '../../../../global/helpers';
import { getAnalyticsController } from '../../controllers';

export const getAnalyticsRoute = Router();

getAnalyticsRoute.get(
  `${GenerateApiVersionPath()}surveys/:surveyId/analytics`,
  ExtractOriginFromRequest,
  BlockRequestByOrigin,
  ExtractIPAddressFromOrigin,
  ExtractCountryFromIPAddress,
  BlockLoggedOutAccount,
  ExtractAccountIdFromRequest,
  BlockNonExistentAccountById,
  validateGetSurveyPayload,
  ValidateSurveyOwnership,
  formatGetSurveyPayload,
  getAnalyticsController,
);
