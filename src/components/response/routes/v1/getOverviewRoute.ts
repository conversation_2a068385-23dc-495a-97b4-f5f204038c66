import { Router } from 'express';
import {
  ExtractOriginFromRequest,
  BlockRequestByOrigin,
  ExtractIPAddressFromOrigin,
  ExtractCountryFromIPAddress,
  BlockLoggedOutAccount,
  ExtractAccountIdFromRequest,
  BlockNonExistentAccountById,
  ValidateSurveyOwnership,
} from '../../../../global/middlewares';
import { validateGetSurveyPayload, formatGetSurveyPayload } from '../../../survey/middlewares';
import { GenerateApiVersionPath } from '../../../../global/helpers';
import { getOverviewController } from '../../controllers';

export const getOverviewRoute = Router();

getOverviewRoute.get(
  `${GenerateApiVersionPath()}surveys/:surveyId/overview`,
  ExtractOriginFromRequest,
  BlockRequestByOrigin,
  ExtractIPAddressFromOrigin,
  ExtractCountryFromIPAddress,
  BlockLoggedOutAccount,
  ExtractAccountIdFromRequest,
  BlockNonExistentAccountById,
  validateGetSurveyPayload,
  ValidateSurveyOwnership,
  formatGetSurveyPayload,
  getOverviewController,
);
