import { Request, Response } from 'express';

import { fetchAnalyticsData } from '../dals';
import { verifyResourceOwnership } from '../../security/helpers';
import { processTimeFilter, generateAnalyticsFromData } from '../helpers/analyticsHelpers';
import { logger } from '../../../global/services';

export const getAnalyticsController = async (req: Request, res: Response) => {
  const surveyId = res.locals.surveyId;
  const accountId = res.locals.accountId;

  // Extract query parameters for filtering
  const timeFilter = req.query.timeFilter as string;
  const customStartDate = req.query.customStartDate as string;
  const customEndDate = req.query.customEndDate as string;

  logger.info('Analytics request', {
    surveyId: surveyId.substring(0, 8) + '...',
    accountId: accountId.substring(0, 8) + '...',
    filters: { timeFilter, customStartDate, customEndDate },
    ip: req.ip,
  });

  // SECURITY FIX: Double authorization check - middleware + controller level
  const isOwner = await verifyResourceOwnership('survey', surveyId, accountId);
  if (!isOwner) {
    logger.warn('Unauthorized analytics access attempt', {
      surveyId: surveyId.substring(0, 8) + '...',
      accountId: accountId.substring(0, 8) + '...',
      ip: req.ip,
      userAgent: req.headers['user-agent'],
    });
    return res.status(403).json({
      success: false,
      message: 'Access denied',
    });
  }

  // Process time filter and generate where conditions
  const { whereConditions, previousPeriodConditions, trendLabel, headerText } = processTimeFilter(surveyId, accountId, timeFilter, customStartDate, customEndDate);

  // Fetch analytics data
  const analyticsResult = await fetchAnalyticsData(whereConditions, previousPeriodConditions);

  if (!analyticsResult.success) {
    return res.status(400).json({
      success: false,
      message: analyticsResult.message,
    });
  }

  // Generate analytics from raw data
  const analyticsData = analyticsResult.payload as any;
  const analytics = generateAnalyticsFromData(analyticsData.currentPeriodResponses, analyticsData.previousPeriodResponses, trendLabel, timeFilter, headerText);

  // Prepare analytics-specific data with additional insights
  const analyticsPayload = {
    totalResponses: analyticsData.currentPeriodResponses.length,
    avgCompletionTime: analytics.avgCompletionTime,
    avgResponsesPerDay: analytics.avgResponsesPerDay,
    responseRateTrendComponents: analytics.responseRateTrendComponents,
    responsesByDay: analytics.responsesByDay,
    headerText: analytics.headerText,
    oldestResponseTimestamp: analytics.oldestResponseTimestamp,
    // New aggregated data for the cards
    responsesByLanguage: analytics.responsesByLanguage,
    responsesByPlatform: analytics.responsesByPlatform,
    responsesByTimezone: analytics.responsesByTimezone,
    responsesByRegion: analytics.responsesByRegion,
    // Additional analytics-specific data
    timeFilter: timeFilter || 'all-time',
    trendLabel,
    rawCurrentPeriodCount: analyticsData.currentPeriodResponses.length,
    rawPreviousPeriodCount: analyticsData.previousPeriodResponses.length,
  };

  logger.info('Analytics data retrieved successfully', {
    surveyId: surveyId.substring(0, 8) + '...',
    accountId: accountId.substring(0, 8) + '...',
    totalResponses: analyticsPayload.totalResponses,
    timeFilter: analyticsPayload.timeFilter,
    ip: req.ip,
  });

  return res.status(200).json({
    success: true,
    message: 'Analytics data retrieved successfully',
    payload: analyticsPayload,
  });
};
