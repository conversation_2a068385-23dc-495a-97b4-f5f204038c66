import { fetchResponsesForExport, createExportSuccessResponse, createExportErrorResponse, createExportMetadata, ExportOptions, ResponseData } from './exportResponsesBase';

export const exportResponsesAsJson = async (surveyId: string, accountId: string, includeDeleted: boolean = false, whereConditions?: any) => {
  try {
    const options: ExportOptions = {
      surveyId,
      accountId,
      includeDeleted,
      whereConditions,
    };

    const fetchResult = await fetchResponsesForExport(options);

    if (!fetchResult.success) {
      return createExportErrorResponse('json', fetchResult.data);
    }

    const responses = fetchResult.data;

    // Type guard to ensure responses is not null
    if (!responses) {
      return createExportErrorResponse('json', 'No response data available');
    }

    // Process responses for JSON export (responses are already processed from fetchResponsesForExport)
    const processedResponses = responses.map((response: ResponseData) => {
      return {
        id: response.id,
        survey_id: response.survey_id,
        account_id: response.account_id,
        response_data: response.response_data,
        respondent_details: response.respondent_details,
        meta: response.meta,
        created_at: response.created_at,
        updated_at: response.updated_at,
        is_deleted: response.is_deleted,
        delete_reason: response.delete_reason,
      };
    });

    const jsonData = {
      export_metadata: createExportMetadata(surveyId, processedResponses.length, includeDeleted, 'json'),
      responses: processedResponses,
    };

    const jsonString = JSON.stringify(jsonData, null, 2);

    return createExportSuccessResponse(surveyId, 'json', jsonString);
  } catch (error) {
    return createExportErrorResponse('json', error);
  }
};
