import { Op } from 'sequelize';

/**
 * Helper functions for processing analytics data
 * These functions contain business logic for time filtering and analytics calculations
 */

export interface TimeFilterResult {
  whereConditions: any;
  previousPeriodConditions?: any;
  trendLabel: string;
  headerText: string;
}

/**
 * Process time filter and generate where conditions for database queries
 *
 * @param surveyId - Survey ID
 * @param accountId - Account ID
 * @param timeFilter - Time filter string
 * @param customStartDate - Custom start date (for custom range)
 * @param customEndDate - Custom end date (for custom range)
 * @returns Object with where conditions and trend label
 */
export const processTimeFilter = (surveyId: string, accountId: string, timeFilter?: string, customStartDate?: string, customEndDate?: string): TimeFilterResult => {
  // Base where conditions
  const baseConditions = {
    survey_id: surveyId,
    account_id: accountId,
    is_deleted: false,
  };

  let whereConditions: any = { ...baseConditions };
  let previousPeriodConditions: any | undefined;
  let trendLabel = '';
  let headerText = '';

  // Apply time filter if provided
  if (timeFilter && timeFilter !== 'all-time') {
    if (timeFilter === 'custom-range' && customStartDate && customEndDate) {
      // Handle custom date range
      // IMPORTANT: HTML date inputs provide YYYY-MM-DD format
      // When creating Date objects, ensure we handle timezone correctly
      const currentPeriodStart = new Date(customStartDate + 'T00:00:00.000Z');
      const currentPeriodEnd = new Date(customEndDate + 'T23:59:59.999Z');

      whereConditions.created_at = {
        [Op.between]: [currentPeriodStart, currentPeriodEnd],
      };

      // Calculate previous period of same duration
      const rangeDuration = currentPeriodEnd.getTime() - currentPeriodStart.getTime();
      const previousPeriodEnd = new Date(currentPeriodStart.getTime() - 1); // 1ms before current period
      const previousPeriodStart = new Date(previousPeriodEnd.getTime() - rangeDuration);

      previousPeriodConditions = {
        ...baseConditions,
        created_at: {
          [Op.between]: [previousPeriodStart, previousPeriodEnd],
        },
      };

      trendLabel = 'vs previous period';
      headerText = 'CUSTOM PERIOD TREND';
    } else {
      // Handle predefined time filters
      const now = new Date();
      let currentPeriodStart: Date;
      let previousPeriodStart: Date;
      let previousPeriodEnd: Date;

      switch (timeFilter) {
        case 'last-24-hours':
          currentPeriodStart = new Date(now.getTime() - 24 * 60 * 60 * 1000);
          previousPeriodEnd = new Date(currentPeriodStart.getTime() - 1);
          previousPeriodStart = new Date(previousPeriodEnd.getTime() - 24 * 60 * 60 * 1000);
          trendLabel = 'vs yesterday';
          headerText = '24-HOUR TREND';
          break;
        case '7-days':
          currentPeriodStart = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          previousPeriodEnd = new Date(currentPeriodStart.getTime() - 1);
          previousPeriodStart = new Date(previousPeriodEnd.getTime() - 7 * 24 * 60 * 60 * 1000);
          trendLabel = 'vs previous 7 days';
          headerText = '7-DAY TREND';
          break;
        case '30-days':
          currentPeriodStart = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          previousPeriodEnd = new Date(currentPeriodStart.getTime() - 1);
          previousPeriodStart = new Date(previousPeriodEnd.getTime() - 30 * 24 * 60 * 60 * 1000);
          trendLabel = 'vs previous 30 days';
          headerText = '30-DAY TREND';
          break;
        case '90-days':
          currentPeriodStart = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
          previousPeriodEnd = new Date(currentPeriodStart.getTime() - 1);
          previousPeriodStart = new Date(previousPeriodEnd.getTime() - 90 * 24 * 60 * 60 * 1000);
          trendLabel = 'vs previous 90 days';
          headerText = '90-DAY TREND';
          break;
        default:
          currentPeriodStart = new Date(0);
          previousPeriodStart = new Date(0);
          previousPeriodEnd = new Date(0);
          trendLabel = '';
          headerText = 'RESPONSE RATE TREND';
      }

      whereConditions.created_at = {
        [Op.gte]: currentPeriodStart,
      };

      if (trendLabel) {
        previousPeriodConditions = {
          ...baseConditions,
          created_at: {
            [Op.between]: [previousPeriodStart, previousPeriodEnd],
          },
        };
      }
    }
  } else {
    // For all-time filter
    headerText = 'AVG. RESPONSES PER DAY';
  }

  return {
    whereConditions,
    previousPeriodConditions,
    trendLabel,
    headerText,
  };
};

/**
 * Generate analytics from raw response data
 *
 * @param currentPeriodResponses - Array of current period response data
 * @param previousPeriodResponses - Array of previous period response data
 * @param trendLabel - Label for trend comparison
 * @param timeFilter - Original time filter for special handling
 * @param headerText - Dynamic header text for the trend display
 * @returns Analytics object
 */
export const generateAnalyticsFromData = (currentPeriodResponses: any[], previousPeriodResponses: any[], trendLabel: string, timeFilter?: string, headerText?: string) => {
  let avgCompletionTime = 0;
  let avgResponsesPerDay = 0;
  const responsesByDay: any = {};

  // Initialize aggregation objects for the new cards
  const responsesByLanguage: any = {};
  const responsesByPlatform: any = {};
  const responsesByTimezone: any = {};

  // Process current period responses
  currentPeriodResponses.forEach(response => {
    // Calculate completion time if available in meta
    if (response.meta?.completionTime) {
      avgCompletionTime += response.meta.completionTime;
    }

    // Group by day for chart data
    const dayKey = response.created_at.toISOString().split('T')[0];
    responsesByDay[dayKey] = (responsesByDay[dayKey] || 0) + 1;

    // Aggregate data for the new cards
    if (response.meta?.userAgent) {
      // Language aggregation
      const language = response.meta.userAgent.language || 'Unknown';
      responsesByLanguage[language] = (responsesByLanguage[language] || 0) + 1;

      // Platform aggregation
      const platform = response.meta.userAgent.platform || 'Unknown';
      responsesByPlatform[platform] = (responsesByPlatform[platform] || 0) + 1;

      // Timezone aggregation
      const timezone = response.meta.userAgent.timezone || 'Unknown';
      responsesByTimezone[timezone] = (responsesByTimezone[timezone] || 0) + 1;
    } else {
      // Handle cases where userAgent data is not available
      responsesByLanguage['Unknown'] = (responsesByLanguage['Unknown'] || 0) + 1;
      responsesByPlatform['Unknown'] = (responsesByPlatform['Unknown'] || 0) + 1;
      responsesByTimezone['Unknown'] = (responsesByTimezone['Unknown'] || 0) + 1;
    }
  });

  // Calculate average completion time
  if (currentPeriodResponses.length > 0 && avgCompletionTime > 0) {
    avgCompletionTime = Math.round(avgCompletionTime / currentPeriodResponses.length);
  }

  // Calculate average responses per day
  if (currentPeriodResponses.length > 0) {
    const firstResponseDate = new Date(currentPeriodResponses[0].created_at);
    const daysSinceFirst = Math.max(1, Math.ceil((new Date().getTime() - firstResponseDate.getTime()) / (24 * 60 * 60 * 1000)));
    avgResponsesPerDay = Math.floor(currentPeriodResponses.length / daysSinceFirst);
  }

  // Calculate response rate trend - broken into components for better frontend styling
  let responseRateTrendComponents = {
    direction: 'neutral' as 'positive' | 'negative' | 'neutral',
    magnitude: 0,
    comparisonText: '',
  };

  if (timeFilter && timeFilter !== 'all-time' && trendLabel) {
    const previousPeriodCount = previousPeriodResponses.length;

    // Calculate trend with edge case handling
    if (previousPeriodCount === 0 && currentPeriodResponses.length === 0) {
      responseRateTrendComponents = {
        direction: 'neutral',
        magnitude: 0,
        comparisonText: 'No responses yet',
      };
    } else if (previousPeriodCount === 0 && currentPeriodResponses.length > 0) {
      responseRateTrendComponents = {
        direction: 'positive',
        magnitude: 0,
        comparisonText: 'New responses this period',
      };
    } else if (previousPeriodCount > 0 && currentPeriodResponses.length === 0) {
      responseRateTrendComponents = {
        direction: 'negative',
        magnitude: 100,
        comparisonText: trendLabel,
      };
    } else if (previousPeriodCount === currentPeriodResponses.length) {
      // For neutral (no change), show average responses per day instead
      responseRateTrendComponents = {
        direction: 'neutral',
        magnitude: avgResponsesPerDay,
        comparisonText: 'responses/day avg',
      };
    } else {
      const percentChange = Math.round(((currentPeriodResponses.length - previousPeriodCount) / previousPeriodCount) * 100);
      responseRateTrendComponents = {
        direction: percentChange > 0 ? 'positive' : 'negative',
        magnitude: Math.abs(percentChange),
        comparisonText: trendLabel,
      };
    }
  } else if (timeFilter === 'all-time') {
    // For all-time, show average responses per day
    if (currentPeriodResponses.length > 0) {
      const firstResponseDate = new Date(currentPeriodResponses[0].created_at);
      const daysSinceFirst = Math.max(1, Math.ceil((new Date().getTime() - firstResponseDate.getTime()) / (24 * 60 * 60 * 1000)));
      const avgPerDay = Math.floor(currentPeriodResponses.length / daysSinceFirst);
      responseRateTrendComponents = {
        direction: 'neutral',
        magnitude: avgPerDay,
        comparisonText: 'responses/day avg',
      };
    } else {
      responseRateTrendComponents = {
        direction: 'neutral',
        magnitude: 0,
        comparisonText: 'No responses yet',
      };
    }
  }

  // Convert responsesByDay object to array format with dates and counts
  const responsesByDayArray = Object.entries(responsesByDay)
    .map(([date, count]) => ({
      date,
      count,
    }))
    .sort((a, b) => a.date.localeCompare(b.date)); // Sort by date ascending

  // Get the oldest response timestamp for date range setting
  const oldestResponseTimestamp = currentPeriodResponses.length > 0 ? currentPeriodResponses[0].created_at.toISOString() : null;

  // Convert aggregated data to arrays with percentages, sorted by count (descending)
  const totalResponses = currentPeriodResponses.length;

  const responsesByLanguageArray = Object.entries(responsesByLanguage)
    .map(([language, count]) => ({
      language,
      count,
      percentage: totalResponses > 0 ? Math.round(((count as number) / totalResponses) * 100) : 0,
    }))
    .sort((a, b) => (b.count as number) - (a.count as number))
    .slice(0, 5);

  const responsesByPlatformArray = Object.entries(responsesByPlatform)
    .map(([platform, count]) => ({
      platform,
      count,
      percentage: totalResponses > 0 ? Math.round(((count as number) / totalResponses) * 100) : 0,
    }))
    .sort((a, b) => (b.count as number) - (a.count as number))
    .slice(0, 5);

  const responsesByTimezoneArray = Object.entries(responsesByTimezone)
    .map(([timezone, count]) => ({
      timezone,
      count,
      percentage: totalResponses > 0 ? Math.round(((count as number) / totalResponses) * 100) : 0,
    }))
    .sort((a, b) => (b.count as number) - (a.count as number))
    .slice(0, 5);

  return {
    avgCompletionTime,
    avgResponsesPerDay,
    responsesByDay: responsesByDayArray,
    oldestResponseTimestamp,
    responseRateTrendComponents,
    headerText: headerText || 'RESPONSE RATE TREND',
    // New aggregated data for the cards
    responsesByLanguage: responsesByLanguageArray,
    responsesByPlatform: responsesByPlatformArray,
    responsesByTimezone: responsesByTimezoneArray,
  };
};

/**
 * Generate responses by language analytics
 *
 * @param responses - Array of response data
 * @returns Top 5 languages with percentage share
 */
export const generateResponsesByLanguage = (responses: any[]) => {
  const languageCounts: { [key: string]: number } = {};
  const totalResponses = responses.length;

  responses.forEach(response => {
    const language = response.meta?.language || response.meta?.userAgent?.language || 'Unknown';
    languageCounts[language] = (languageCounts[language] || 0) + 1;
  });

  return Object.entries(languageCounts)
    .map(([language, count]) => ({
      language,
      count,
      percentage: Math.round((count / totalResponses) * 100),
    }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 5);
};

/**
 * Generate responses by platform analytics
 *
 * @param responses - Array of response data
 * @returns Top 5 platforms with percentage share
 */
export const generateResponsesByPlatform = (responses: any[]) => {
  const platformCounts: { [key: string]: number } = {};
  const totalResponses = responses.length;

  responses.forEach(response => {
    const platform = response.meta?.platform || response.meta?.userAgent?.platform || 'Unknown';
    platformCounts[platform] = (platformCounts[platform] || 0) + 1;
  });

  return Object.entries(platformCounts)
    .map(([platform, count]) => ({
      platform,
      count,
      percentage: Math.round((count / totalResponses) * 100),
    }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 5);
};

/**
 * Generate responses by timezone analytics
 *
 * @param responses - Array of response data
 * @returns Top 5 timezones with percentage share
 */
export const generateResponsesByTimezone = (responses: any[]) => {
  const timezoneCounts: { [key: string]: number } = {};
  const totalResponses = responses.length;

  responses.forEach(response => {
    const timezone = response.meta?.timezone || response.meta?.userAgent?.timezone || 'Unknown';
    timezoneCounts[timezone] = (timezoneCounts[timezone] || 0) + 1;
  });

  return Object.entries(timezoneCounts)
    .map(([timezone, count]) => ({
      timezone,
      count,
      percentage: Math.round((count / totalResponses) * 100),
    }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 5);
};

/**
 * Generate responses by region/country analytics
 *
 * @param responses - Array of response data
 * @returns All regions with percentage share and flags
 */
export const generateResponsesByRegion = (responses: any[]) => {
  const { getCountryInfo } = require('../../../global/var/CountryFlags');
  const regionCounts: { [key: string]: number } = {};
  const totalResponses = responses.length;

  responses.forEach(response => {
    const region = response.meta?.country || response.meta?.region || 'Unknown';
    regionCounts[region] = (regionCounts[region] || 0) + 1;
  });

  // Process regions and combine all "Unknown" entries into a single category
  const processedRegions: { [key: string]: { count: number; countryInfo: any } } = {};

  Object.entries(regionCounts).forEach(([region, count]) => {
    const countryInfo = getCountryInfo(region);

    // If the country info resolves to "Unknown", combine all such entries
    if (countryInfo.name === 'Unknown') {
      if (!processedRegions['Unknown']) {
        processedRegions['Unknown'] = { count: 0, countryInfo };
      }
      processedRegions['Unknown'].count += count;
    } else {
      // For known countries, use the original region as key to avoid duplicates
      processedRegions[region] = { count, countryInfo };
    }
  });

  return Object.entries(processedRegions)
    .map(([_, { count, countryInfo }]) => ({
      name: countryInfo.name,
      flag: countryInfo.flag,
      percentage: Math.round((count / totalResponses) * 1000) / 10, // Round to 1 decimal place
    }))
    .sort((a, b) => b.percentage - a.percentage); // Sort by percentage descending
};
