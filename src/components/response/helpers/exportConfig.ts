/**
 * Centralized export configuration
 * Eliminates hardcoded format mappings scattered across the codebase
 */

export interface ExportFormatConfig {
  contentType: string;
  fileExtension: string;
  estimatedSizeMultiplier: number; // KB per response estimate
}

export const EXPORT_FORMATS: Record<string, ExportFormatConfig> = {
  csv: {
    contentType: 'text/csv',
    fileExtension: 'csv',
    estimatedSizeMultiplier: 0.5,
  },
  json: {
    contentType: 'application/json',
    fileExtension: 'json',
    estimatedSizeMultiplier: 1.2,
  },
  pdf: {
    contentType: 'application/pdf',
    fileExtension: 'pdf',
    estimatedSizeMultiplier: 2.0,
  },
};

export const SUPPORTED_FORMATS = Object.keys(EXPORT_FORMATS);

/**
 * Get format configuration by format name
 */
export const getFormatConfig = (format: string): ExportFormatConfig | null => {
  return EXPORT_FORMATS[format] || null;
};

/**
 * Check if format is supported
 */
export const isFormatSupported = (format: string): boolean => {
  return SUPPORTED_FORMATS.includes(format);
};

/**
 * Get estimated file size for a format
 */
export const getEstimatedFileSize = (format: string, responseCount: number): string => {
  const config = getFormatConfig(format);
  if (!config) return '0KB';
  
  const sizeKB = Math.ceil(responseCount * config.estimatedSizeMultiplier);
  return `${sizeKB}KB`;
};

/**
 * Generate export URLs with query parameters
 */
export const generateExportUrl = (
  surveyId: string, 
  format: string, 
  timeFilter?: string, 
  customStartDate?: string, 
  customEndDate?: string
): string => {
  const baseUrl = `/api/v1/surveys/${surveyId}/responses/export`;
  const params = new URLSearchParams();
  
  params.append('format', format);
  if (timeFilter) params.append('timeFilter', timeFilter);
  if (customStartDate) params.append('customStartDate', customStartDate);
  if (customEndDate) params.append('customEndDate', customEndDate);
  
  return `${baseUrl}?${params.toString()}`;
};

/**
 * Generate all export URLs for supported formats
 */
export const generateAllExportUrls = (
  surveyId: string,
  timeFilter?: string,
  customStartDate?: string,
  customEndDate?: string
): Record<string, string> => {
  const urls: Record<string, string> = {};
  
  SUPPORTED_FORMATS.forEach(format => {
    urls[format] = generateExportUrl(surveyId, format, timeFilter, customStartDate, customEndDate);
  });
  
  return urls;
};

/**
 * Generate estimated file sizes for all formats
 */
export const generateAllEstimatedSizes = (responseCount: number): Record<string, string> => {
  const sizes: Record<string, string> = {};
  
  SUPPORTED_FORMATS.forEach(format => {
    sizes[format] = getEstimatedFileSize(format, responseCount);
  });
  
  return sizes;
};
