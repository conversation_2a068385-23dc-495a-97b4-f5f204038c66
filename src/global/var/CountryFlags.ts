/**
 * Global constants for country codes and their corresponding flags
 * Used for analytics and regional data display
 */

export interface CountryFlag {
  name: string;
  flag: string;
}

/**
 * Mapping of country codes to their flags and names
 * Based on ISO 3166-1 alpha-2 country codes
 */
export const CountryFlags: { [key: string]: CountryFlag } = {
  // Popular countries first
  US: { name: 'United States', flag: '🇺🇸' },
  IN: { name: 'India', flag: '🇮🇳' },
  GB: { name: 'United Kingdom', flag: '🇬🇧' },
  CA: { name: 'Canada', flag: '🇨🇦' },
  AU: { name: 'Australia', flag: '🇦🇺' },
  DE: { name: 'Germany', flag: '🇩🇪' },
  FR: { name: 'France', flag: '🇫🇷' },
  JP: { name: 'Japan', flag: '🇯🇵' },
  CN: { name: 'China', flag: '🇨🇳' },
  BR: { name: 'Brazil', flag: '🇧🇷' },
  MX: { name: 'Mexico', flag: '🇲🇽' },
  IT: { name: 'Italy', flag: '🇮🇹' },
  ES: { name: 'Spain', flag: '🇪🇸' },
  NL: { name: 'Netherlands', flag: '🇳🇱' },
  SE: { name: 'Sweden', flag: '🇸🇪' },
  NO: { name: 'Norway', flag: '🇳🇴' },
  DK: { name: 'Denmark', flag: '🇩🇰' },
  FI: { name: 'Finland', flag: '🇫🇮' },
  CH: { name: 'Switzerland', flag: '🇨🇭' },
  AT: { name: 'Austria', flag: '🇦🇹' },
  BE: { name: 'Belgium', flag: '🇧🇪' },
  IE: { name: 'Ireland', flag: '🇮🇪' },
  PT: { name: 'Portugal', flag: '🇵🇹' },
  GR: { name: 'Greece', flag: '🇬🇷' },
  PL: { name: 'Poland', flag: '🇵🇱' },
  CZ: { name: 'Czech Republic', flag: '🇨🇿' },
  HU: { name: 'Hungary', flag: '🇭🇺' },
  RO: { name: 'Romania', flag: '🇷🇴' },
  BG: { name: 'Bulgaria', flag: '🇧🇬' },
  HR: { name: 'Croatia', flag: '🇭🇷' },
  SI: { name: 'Slovenia', flag: '🇸🇮' },
  SK: { name: 'Slovakia', flag: '🇸🇰' },
  LT: { name: 'Lithuania', flag: '🇱🇹' },
  LV: { name: 'Latvia', flag: '🇱🇻' },
  EE: { name: 'Estonia', flag: '🇪🇪' },
  RU: { name: 'Russia', flag: '🇷🇺' },
  UA: { name: 'Ukraine', flag: '🇺🇦' },
  BY: { name: 'Belarus', flag: '🇧🇾' },
  MD: { name: 'Moldova', flag: '🇲🇩' },
  TR: { name: 'Turkey', flag: '🇹🇷' },
  IL: { name: 'Israel', flag: '🇮🇱' },
  SA: { name: 'Saudi Arabia', flag: '🇸🇦' },
  AE: { name: 'United Arab Emirates', flag: '🇦🇪' },
  EG: { name: 'Egypt', flag: '🇪🇬' },
  ZA: { name: 'South Africa', flag: '🇿🇦' },
  NG: { name: 'Nigeria', flag: '🇳🇬' },
  KE: { name: 'Kenya', flag: '🇰🇪' },
  GH: { name: 'Ghana', flag: '🇬🇭' },
  MA: { name: 'Morocco', flag: '🇲🇦' },
  TN: { name: 'Tunisia', flag: '🇹🇳' },
  DZ: { name: 'Algeria', flag: '🇩🇿' },
  LY: { name: 'Libya', flag: '🇱🇾' },
  SD: { name: 'Sudan', flag: '🇸🇩' },
  ET: { name: 'Ethiopia', flag: '🇪🇹' },
  UG: { name: 'Uganda', flag: '🇺🇬' },
  TZ: { name: 'Tanzania', flag: '🇹🇿' },
  ZW: { name: 'Zimbabwe', flag: '🇿🇼' },
  ZM: { name: 'Zambia', flag: '🇿🇲' },
  MW: { name: 'Malawi', flag: '🇲🇼' },
  MZ: { name: 'Mozambique', flag: '🇲🇿' },
  BW: { name: 'Botswana', flag: '🇧🇼' },
  NA: { name: 'Namibia', flag: '🇳🇦' },
  SZ: { name: 'Eswatini', flag: '🇸🇿' },
  LS: { name: 'Lesotho', flag: '🇱🇸' },
  KR: { name: 'South Korea', flag: '🇰🇷' },
  TH: { name: 'Thailand', flag: '🇹🇭' },
  VN: { name: 'Vietnam', flag: '🇻🇳' },
  MY: { name: 'Malaysia', flag: '🇲🇾' },
  SG: { name: 'Singapore', flag: '🇸🇬' },
  ID: { name: 'Indonesia', flag: '🇮🇩' },
  PH: { name: 'Philippines', flag: '🇵🇭' },
  BD: { name: 'Bangladesh', flag: '🇧🇩' },
  PK: { name: 'Pakistan', flag: '🇵🇰' },
  LK: { name: 'Sri Lanka', flag: '🇱🇰' },
  NP: { name: 'Nepal', flag: '🇳🇵' },
  BT: { name: 'Bhutan', flag: '🇧🇹' },
  MV: { name: 'Maldives', flag: '🇲🇻' },
  AF: { name: 'Afghanistan', flag: '🇦🇫' },
  IR: { name: 'Iran', flag: '🇮🇷' },
  IQ: { name: 'Iraq', flag: '🇮🇶' },
  SY: { name: 'Syria', flag: '🇸🇾' },
  LB: { name: 'Lebanon', flag: '🇱🇧' },
  JO: { name: 'Jordan', flag: '🇯🇴' },
  KW: { name: 'Kuwait', flag: '🇰🇼' },
  QA: { name: 'Qatar', flag: '🇶🇦' },
  BH: { name: 'Bahrain', flag: '🇧🇭' },
  OM: { name: 'Oman', flag: '🇴🇲' },
  YE: { name: 'Yemen', flag: '🇾🇪' },
  AR: { name: 'Argentina', flag: '🇦🇷' },
  CL: { name: 'Chile', flag: '🇨🇱' },
  PE: { name: 'Peru', flag: '🇵🇪' },
  CO: { name: 'Colombia', flag: '🇨🇴' },
  VE: { name: 'Venezuela', flag: '🇻🇪' },
  EC: { name: 'Ecuador', flag: '🇪🇨' },
  BO: { name: 'Bolivia', flag: '🇧🇴' },
  PY: { name: 'Paraguay', flag: '🇵🇾' },
  UY: { name: 'Uruguay', flag: '🇺🇾' },
  GY: { name: 'Guyana', flag: '🇬🇾' },
  SR: { name: 'Suriname', flag: '🇸🇷' },
  GF: { name: 'French Guiana', flag: '🇬🇫' },
  NZ: { name: 'New Zealand', flag: '🇳🇿' },
  FJ: { name: 'Fiji', flag: '🇫🇯' },
  PG: { name: 'Papua New Guinea', flag: '🇵🇬' },
  NC: { name: 'New Caledonia', flag: '🇳🇨' },
  VU: { name: 'Vanuatu', flag: '🇻🇺' },
  SB: { name: 'Solomon Islands', flag: '🇸🇧' },
  TO: { name: 'Tonga', flag: '🇹🇴' },
  WS: { name: 'Samoa', flag: '🇼🇸' },
  KI: { name: 'Kiribati', flag: '🇰🇮' },
  TV: { name: 'Tuvalu', flag: '🇹🇻' },
  NR: { name: 'Nauru', flag: '🇳🇷' },
  PW: { name: 'Palau', flag: '🇵🇼' },
  FM: { name: 'Micronesia', flag: '🇫🇲' },
  MH: { name: 'Marshall Islands', flag: '🇲🇭' },
};

/**
 * Get country flag and name by country code
 *
 * @param countryCode - ISO 3166-1 alpha-2 country code
 * @returns Country information with flag and name
 */
export const getCountryInfo = (countryCode: string): CountryFlag => {
  const upperCode = countryCode?.toUpperCase();
  return (
    CountryFlags[upperCode] || {
      name: 'Unknown',
      flag: '🏳️',
    }
  );
};
