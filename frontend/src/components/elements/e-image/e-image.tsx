import { Component, Prop, Host, h } from '@stencil/core';
import { LooseObjectInterface } from '../../../global/script/interfaces';

@Component({
  tag: 'e-image',
  styleUrl: 'e-image.css',
  shadow: true,
})
export class EImage {
  @Prop() variant: string = 'default';
  @Prop() src: string = '';
  @Prop() width: string = '100%';
  @Prop() imageTitle: string = '';

  private classMap: LooseObjectInterface = {};

  componentWillLoad() {
    this.generateClassMap();
  }

  generateClassMap() {
    this.classMap.width = this.width;
  }

  render() {
    return (
      <Host>
        <img style={this.classMap} src={this.src} title={this.imageTitle}></img>
      </Host>
    );
  }
}
