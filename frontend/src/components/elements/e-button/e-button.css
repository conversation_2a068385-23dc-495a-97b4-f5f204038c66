/* Default */
button {
  display: flex;
  justify-content: space-around;
  align-items: center;
  background: none;
  border: 0;
  font-size: 1em;
  border-radius: var(--border-radius);
  transition: all 0.25s;
  padding: calc(var(--padding) / 1.5) var(--padding);
  height: 50px;
}
button:hover {
  cursor: pointer;
}
button:disabled {
  opacity: 0.3;
  pointer-events: none;
}

/* Primary Button (Default) */
.button__primary--default {
  font-weight: 600;
  border: 1px solid var(--color__indigo--600);
  background: var(--color__indigo--600);
  color: var(--color__bg);
}
.button__primary--default:hover {
  background: var(--color__indigo--800);
}
.button__primary--default:active {
  background: var(--color__indigo--400);
}
.button__primary--danger {
  background: var(--color__red--800);
  color: var(--color__bg);
}
.button__primary--danger:hover {
  background: var(--color__red--900);
}
.button__primary--danger:active {
  background: var(--color__red--light);
}
.button__primary--success {
  font-weight: 600;
  background: var(--color__green--600);
  color: var(--color__bg);
}
.button__primary--success:hover {
  background: var(--color__green--800);
}
.button__primary--success:active {
  background: var(--color__green--400);
}

/* Ghost Button (Default) */
.button__ghost--default {
  border: 1px solid var(--color__indigo--600);
  color: var(--color__indigo--600);
}
.button__ghost--default:hover {
  background: var(--color__indigo--50);
}
.button__ghost--default:active {
  color: var(--color__indigo--200);
}

/* Link Button (Default) */
.button__link--default {
  padding: 0;
  border: 0;
  color: var(--color__indigo--600);
  height: auto;
}
.button__link--default:hover {
  color: var(--color__indigo--800);
}
.button__link--default:active {
  color: var(--color__indigo--400);
}

/* Link Button (Danger) */
.button__link--danger {
  padding: 0;
  border: 0;
  color: var(--color__red--600);
  height: auto;
}
.button__link--danger:hover {
  color: var(--color__red--800);
}
.button__link--danger:active {
  color: var(--color__red--400);
}

/* Light Button (Default) */
.button__light--default {
  color: var(--color__indigo--600);
}
.button__light--default:hover {
  background: var(--color__grey--50);
  color: var(--color__indigo--800);
}
.button__light--default:active {
  color: var(--color__indigo--200);
}

/*  Button Size */
.button__size--wide {
  display: block;
  width: 100%;
}

.button__size--small {
  padding: calc(var(--padding) / 2) calc(var(--padding) / 1.5);
  height: 40px;
}

/* Button Status */
.button__status--active {
  padding: calc(var(--padding) / 2) calc(var(--padding) / 1.5);
}
