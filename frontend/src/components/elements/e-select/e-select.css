/* Select styling */
select {
  appearance: none; /* Remove default browser styling */
  -webkit-appearance: none;
  -moz-appearance: none;
  padding: var(--padding);
  border: var(--border__input);
  border-radius: var(--border-radius);
  background-color: white;
  color: var(--color__grey--600);
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  font-family: inherit;
  font-size: inherit;
  width: 100%; /* Adjust as needed */
  box-sizing: border-box;

  /* Add custom dropdown arrow */
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right calc(var(--padding) * 0.75) center;
  padding-right: calc(var(--padding) * 2.5); /* Make room for the arrow */
}

select:hover,
select:focus {
  border: 1px solid var(--color__grey--800);
  color: var(--color__grey--800);
  outline: none;
}

select:focus {
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.05);
}

/* For Firefox, which has different handling */
select:-moz-focusring {
  color: transparent;
  text-shadow: 0 0 0 var(--color__grey--800);
}

/* For option elements */
option {
  padding: calc(var(--padding) * 0.5) var(--padding);
  background-color: white;
  color: var(--color__grey--800);
}

/* Selected option styling */
select option:checked {
  background-color: var(--color__grey--100);
  color: var(--color__grey--800);
}

/* Disable the default option appearance in some browsers */
@-moz-document url-prefix() {
  select {
    color: var(--color__grey--800);
    text-indent: 0.01px;
    text-overflow: '';
  }
}

/* Narrow variant */
.narrow {
  padding: 0.5em 1em;
  padding-right: calc(1em + 1.5em); /* Make room for the arrow */
  background-position: right 0.75em center;
}
