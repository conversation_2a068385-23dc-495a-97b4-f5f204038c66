import { Component, h, Prop, State, Element } from '@stencil/core';
import { getResponsesApi, ResponseFilters } from '../p-survey-responses/helpers';

@Component({
  tag: 'p-survey-analytics',
  styleUrl: 'p-survey-analytics.css',
  shadow: true,
})
export class PSurveyAnalytics {
  @Element() el: HTMLElement;
  @Prop() surveyId: string;
  @Prop() survey: any;

  @State() selectedTimeFilter: string = 'all-time';
  @State() customStartDate: string = '';
  @State() customEndDate: string = '';
  @State() firstResponseDate: string = '';
  @State() validationError: string = '';
  @State() totalCount: number = 0;
  @State() loading: boolean = true;
  @State() error: string = null;
  @State() analytics: any = {};

  private timeFilterOptions = [
    { label: 'All time', value: 'all-time' },
    { label: 'Last 7 days', value: '7-days' },
    { label: 'Last 30 days', value: '30-days' },
    { label: 'Last 90 days', value: '90-days' },
    { label: 'Custom Range', value: 'custom-range' },
  ];

  async componentDidLoad() {
    await this.fetchAnalyticsData();
    await this.setFirstResponseDate();
  }

  private async fetchAnalyticsData() {
    if (!this.surveyId) return;

    this.loading = true;
    this.error = null;

    try {
      const filters: ResponseFilters = {
        timeFilter: this.selectedTimeFilter,
        page: 1,
        limit: 1, // We only need the analytics data, not the actual responses
      };

      // Add custom date range if selected
      if (this.selectedTimeFilter === 'custom-range') {
        filters.customStartDate = this.customStartDate;
        filters.customEndDate = this.customEndDate;
      }

      const result = await getResponsesApi(this.surveyId, filters);

      if (result.success) {
        this.totalCount = result.payload.totalCount || 0;
        this.analytics = result.payload || {};
      } else {
        this.error = result.message;
      }
    } catch (error) {
      console.error('Error fetching analytics data:', error);
      this.error = 'Failed to load analytics data';
    } finally {
      this.loading = false;
    }
  }

  private async setFirstResponseDate() {
    try {
      // Get the first response date for date picker constraints
      const result = await getResponsesApi(this.surveyId, {
        timeFilter: 'all-time',
        page: 1,
        limit: 1,
      });
      if (result.success && result.payload.responses && result.payload.responses.length > 0) {
        const firstResponse = result.payload.responses[result.payload.responses.length - 1];
        if (firstResponse && firstResponse.created_at) {
          this.firstResponseDate = new Date(firstResponse.created_at).toISOString().split('T')[0];
        }
      }
    } catch (error) {
      console.error('Error setting first response date:', error);
    }
  }

  private handleTimeFilterChange = async (event: any) => {
    const newValue = event.detail.value;

    if (newValue === 'custom-range') {
      this.selectedTimeFilter = newValue;
      this.customStartDate = '';
      this.customEndDate = '';
      this.validationError = '';
    } else {
      this.selectedTimeFilter = newValue;
      await this.fetchAnalyticsData();
    }
  };

  private applyInlineCustomDateRange = async () => {
    if (!this.customStartDate || !this.customEndDate) {
      this.validationError = 'Please select both start and end dates';
      return;
    }

    if (new Date(this.customStartDate) > new Date(this.customEndDate)) {
      this.validationError = 'Start date must be before end date';
      return;
    }

    this.validationError = '';
    await this.fetchAnalyticsData();
  };

  generateSurveyPill(value: string) {
    if (value === 'sensePrice') {
      return <e-pill color="purple">SensePrice</e-pill>;
    } else if (value === 'senseChoice') {
      return <e-pill color="blue">SenseChoice</e-pill>;
    } else if (value === 'sensePoll') {
      return <e-pill color="indigo">SensePoll</e-pill>;
    } else if (value === 'senseQuery') {
      return <e-pill color="turquoise">SenseQuery</e-pill>;
    } else if (value === 'sensePriority') {
      return <e-pill color="teal">SensePriority</e-pill>;
    }
  }

  render() {
    return (
      <div>
        {/* Header with title and filters */}
        <l-row justifyContent="space-between" align="center">
          <e-text variant="heading">Analytics</e-text>

          {/* Custom Range Date Inputs - shown inline when custom range is selected */}
          {this.selectedTimeFilter === 'custom-range' && (
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.75em',
                flex: '1',
                justifyContent: 'center',
              }}
            >
              <input
                type="date"
                value={this.customStartDate}
                min={this.firstResponseDate}
                max={new Date().toISOString().split('T')[0]}
                onInput={e => {
                  this.customStartDate = (e.target as HTMLInputElement).value;
                  this.validationError = '';
                  // Auto-apply when both dates are set
                  if (this.customStartDate && this.customEndDate) {
                    this.applyInlineCustomDateRange();
                  }
                }}
                style={{
                  padding: '0.5em',
                  border: 'var(--border__input)',
                  borderRadius: 'var(--border-radius)',
                  fontSize: '0.875em',
                  width: '140px',
                }}
              />

              <e-text style={{ color: 'var(--color__grey--600)', fontSize: '0.875em' }}>to</e-text>

              <input
                type="date"
                value={this.customEndDate}
                min={this.customStartDate || this.firstResponseDate}
                max={new Date().toISOString().split('T')[0]}
                onInput={e => {
                  this.customEndDate = (e.target as HTMLInputElement).value;
                  this.validationError = '';
                  // Auto-apply when both dates are set
                  if (this.customStartDate && this.customEndDate) {
                    this.applyInlineCustomDateRange();
                  }
                }}
                style={{
                  padding: '0.5em',
                  border: 'var(--border__input)',
                  borderRadius: 'var(--border-radius)',
                  fontSize: '0.875em',
                  width: '140px',
                }}
              />
            </div>
          )}

          <div style={{ position: 'relative' }}>
            <e-select
              value={this.selectedTimeFilter}
              options={JSON.stringify(this.timeFilterOptions)}
              name="timeFilter"
              variant="narrow"
              onSelectChangeEvent={this.handleTimeFilterChange}
            />
          </div>
        </l-row>

        {/* Validation Error for inline custom range */}
        {this.selectedTimeFilter === 'custom-range' && this.validationError && (
          <div style={{ marginTop: '0.5em', textAlign: 'center' }}>
            <e-text
              style={{
                color: 'var(--color__red--600)',
                fontSize: '0.875em',
                display: 'inline-block',
                padding: '0.5em 1em',
                backgroundColor: 'var(--color__red--50)',
                border: '1px solid var(--color__red--200)',
                borderRadius: 'var(--border-radius)',
              }}
            >
              {this.validationError}
            </e-text>
          </div>
        )}

        <l-spacer value={2}></l-spacer>

        {/* Error State */}
        {this.error && (
          <c-card>
            <div style={{ textAlign: 'center', padding: '2em' }}>
              <e-text>
                <strong>{this.error}</strong>
              </e-text>
            </div>
          </c-card>
        )}

        {/* Analytics Content */}
        {!this.error && (
          <c-card>
            <l-spacer value={1}></l-spacer>
            <e-text>
              Charts and graphs showing response patterns over time will be displayed here.
              {this.selectedTimeFilter !== 'all-time' &&
                ` Filtered for ${this.selectedTimeFilter.replace('-', ' ')}.`}
            </e-text>
            <l-spacer value={1}></l-spacer>
            <div
              style={{
                height: '200px',
                backgroundColor: 'var(--color__grey--50)',
                borderRadius: '4px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                flexDirection: 'column',
                gap: '0.5em',
              }}
            >
              <e-text variant="footnote">Analytics visualization placeholder</e-text>
              {!this.loading && (
                <e-text variant="footnote" style={{ color: 'var(--color__grey--600)' }}>
                  {this.totalCount} response{this.totalCount !== 1 ? 's' : ''} in selected time
                  range
                </e-text>
              )}
            </div>
            <l-spacer value={1}></l-spacer>
            <e-text variant="footnote">
              Analytics will show response trends, completion rates over time, and demographic
              breakdowns once you have survey responses.
            </e-text>
          </c-card>
        )}
      </div>
    );
  }
}
