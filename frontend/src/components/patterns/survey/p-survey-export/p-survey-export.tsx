import { Component, h, Prop, State, Element } from '@stencil/core';
import { getResponsesApi, ResponseFilters } from '../p-survey-responses/helpers';
import { FormatNumber } from '../../../../global/script/helpers';

@Component({
  tag: 'p-survey-export',
  styleUrl: 'p-survey-export.css',
  shadow: true,
})
export class PSurveyExport {
  @Element() el: HTMLElement;
  @Prop() surveyId: string;
  @Prop() survey: any;

  @State() selectedTimeFilter: string = 'all-time';
  @State() customStartDate: string = '';
  @State() customEndDate: string = '';
  @State() firstResponseDate: string = '';
  @State() validationError: string = '';
  @State() totalCount: number = 0;
  @State() loading: boolean = true;
  @State() error: string = null;
  @State() selectedFormat: string = 'csv';
  @State() exportRequests: any[] = [];
  @State() isSubmittingRequest: boolean = false;

  private timeFilterOptions = [
    { label: 'All time', value: 'all-time' },
    { label: 'Last 7 days', value: '7-days' },
    { label: 'Last 30 days', value: '30-days' },
    { label: 'Last 90 days', value: '90-days' },
    { label: 'Custom Range', value: 'custom-range' },
  ];

  private formatOptions = [
    { label: 'CSV', value: 'csv' },
    { label: 'PDF', value: 'pdf' },
    { label: 'JSON', value: 'json' },
  ];

  async componentDidLoad() {
    await this.fetchResponseCount();
    await this.setFirstResponseDate();
    await this.fetchExportRequests();

    // Poll for export request updates every 5 seconds
    setInterval(() => {
      this.fetchExportRequests();
    }, 5000);
  }

  private async fetchResponseCount() {
    if (!this.surveyId) return;

    this.loading = true;
    this.error = null;

    try {
      const filters: ResponseFilters = {
        timeFilter: this.selectedTimeFilter,
        page: 1,
        limit: 1, // We only need the count, not the actual responses
      };

      // Add custom date range if selected
      if (this.selectedTimeFilter === 'custom-range') {
        filters.customStartDate = this.customStartDate;
        filters.customEndDate = this.customEndDate;
      }

      const result = await getResponsesApi(this.surveyId, filters);

      if (result.success) {
        this.totalCount = result.payload.totalCount || 0;
      } else {
        this.error = result.message;
      }
    } catch (error) {
      console.error('Error fetching response count:', error);
      this.error = 'Failed to load response count';
    } finally {
      this.loading = false;
    }
  }

  private async setFirstResponseDate() {
    try {
      // Get the first response date for date picker constraints
      const result = await getResponsesApi(this.surveyId, {
        timeFilter: 'all-time',
        page: 1,
        limit: 1,
      });
      if (result.success && result.payload.responses && result.payload.responses.length > 0) {
        const firstResponse = result.payload.responses[result.payload.responses.length - 1];
        if (firstResponse && firstResponse.created_at) {
          this.firstResponseDate = new Date(firstResponse.created_at).toISOString().split('T')[0];
        }
      }
    } catch (error) {
      console.error('Error setting first response date:', error);
    }
  }

  private handleTimeFilterChange = async (event: any) => {
    const newValue = event.detail.value;

    if (newValue === 'custom-range') {
      this.selectedTimeFilter = newValue;
      this.customStartDate = '';
      this.customEndDate = '';
      this.validationError = '';
    } else {
      this.selectedTimeFilter = newValue;
      await this.fetchResponseCount();
    }
  };

  private applyInlineCustomDateRange = async () => {
    if (!this.customStartDate || !this.customEndDate) {
      this.validationError = 'Please select both start and end dates';
      return;
    }

    if (new Date(this.customStartDate) > new Date(this.customEndDate)) {
      this.validationError = 'Start date must be before end date';
      return;
    }

    this.validationError = '';
    await this.fetchResponseCount();
  };

  private handleFormatChange = (event: any) => {
    this.selectedFormat = event.detail.value;
  };

  private async fetchExportRequests() {
    try {
      // This would be an API call to get export requests for this survey
      // For now, using mock data structure
      // const result = await getExportRequestsApi(this.surveyId);

      // Mock data for demonstration
      this.exportRequests = [
        // {
        //   id: 'export-123',
        //   format: 'csv',
        //   status: 'completed',
        //   createdAt: new Date().toISOString(),
        //   downloadUrl: '/downloads/export-123.csv',
        //   filters: { timeFilter: 'all-time' }
        // }
      ];
    } catch (error) {
      console.error('Error fetching export requests:', error);
    }
  }

  private submitExportRequest = async () => {
    if (this.totalCount === 0 || this.isSubmittingRequest) return;

    this.isSubmittingRequest = true;

    try {
      // Build export request payload
      const requestPayload = {
        format: this.selectedFormat,
        filters: {
          timeFilter: this.selectedTimeFilter,
          customStartDate: this.customStartDate,
          customEndDate: this.customEndDate,
        },
      };

      // This would be an API call to submit export request
      // const result = await submitExportRequestApi(this.surveyId, requestPayload);

      // Mock successful submission
      const mockRequest = {
        id: `export-${Date.now()}`,
        format: this.selectedFormat,
        status: 'processing',
        createdAt: new Date().toISOString(),
        downloadUrl: null,
        filters: requestPayload.filters,
      };

      this.exportRequests = [mockRequest, ...this.exportRequests];

      alert(
        "Export request submitted successfully! You will be notified when it's ready for download.",
      );
    } catch (error) {
      console.error('Error submitting export request:', error);
      alert(`Failed to submit export request. Please try again.`);
    } finally {
      this.isSubmittingRequest = false;
    }
  };

  private getStatusBadgeColor = (status: string): string => {
    switch (status) {
      case 'completed':
        return 'var(--color__green--600)';
      case 'processing':
        return 'var(--color__blue--600)';
      case 'failed':
        return 'var(--color__red--600)';
      default:
        return 'var(--color__grey--600)';
    }
  };

  private formatRequestDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  render() {
    return (
      <div>
        {/* Header with title and filters */}
        <l-row justifyContent="space-between" align="center">
          <e-text variant="heading">Export</e-text>

          {/* Custom Range Date Inputs - shown inline when custom range is selected */}
          {this.selectedTimeFilter === 'custom-range' && (
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.75em',
                flex: '1',
                justifyContent: 'center',
              }}
            >
              <input
                type="date"
                value={this.customStartDate}
                min={this.firstResponseDate}
                max={new Date().toISOString().split('T')[0]}
                onInput={e => {
                  this.customStartDate = (e.target as HTMLInputElement).value;
                  this.validationError = '';
                  // Auto-apply when both dates are set
                  if (this.customStartDate && this.customEndDate) {
                    this.applyInlineCustomDateRange();
                  }
                }}
                style={{
                  padding: '0.5em',
                  border: 'var(--border__input)',
                  borderRadius: 'var(--border-radius)',
                  fontSize: '0.875em',
                  width: '140px',
                }}
              />

              <e-text style={{ color: 'var(--color__grey--600)', fontSize: '0.875em' }}>to</e-text>

              <input
                type="date"
                value={this.customEndDate}
                min={this.customStartDate || this.firstResponseDate}
                max={new Date().toISOString().split('T')[0]}
                onInput={e => {
                  this.customEndDate = (e.target as HTMLInputElement).value;
                  this.validationError = '';
                  // Auto-apply when both dates are set
                  if (this.customStartDate && this.customEndDate) {
                    this.applyInlineCustomDateRange();
                  }
                }}
                style={{
                  padding: '0.5em',
                  border: 'var(--border__input)',
                  borderRadius: 'var(--border-radius)',
                  fontSize: '0.875em',
                  width: '140px',
                }}
              />
            </div>
          )}

          <div style={{ position: 'relative' }}>
            <e-select
              value={this.selectedTimeFilter}
              options={JSON.stringify(this.timeFilterOptions)}
              name="timeFilter"
              variant="narrow"
              onSelectChangeEvent={this.handleTimeFilterChange}
            />
          </div>
        </l-row>

        {/* Validation Error for inline custom range */}
        {this.selectedTimeFilter === 'custom-range' && this.validationError && (
          <div style={{ marginTop: '0.5em', textAlign: 'center' }}>
            <e-text
              style={{
                color: 'var(--color__red--600)',
                fontSize: '0.875em',
                display: 'inline-block',
                padding: '0.5em 1em',
                backgroundColor: 'var(--color__red--50)',
                border: '1px solid var(--color__red--200)',
                borderRadius: 'var(--border-radius)',
              }}
            >
              {this.validationError}
            </e-text>
          </div>
        )}

        <l-spacer value={2}></l-spacer>

        {/* Error State */}
        {this.error && (
          <c-card>
            <div style={{ textAlign: 'center', padding: '2em' }}>
              <e-text>
                <strong>{this.error}</strong>
              </e-text>
            </div>
          </c-card>
        )}

        {/* Export Options */}
        <c-card>
          {/* Single row layout with all export options */}
          <l-row align="center" justifyContent="space-between" style={{ gap: '1em' }}>
            {/* Total responses text */}
            <e-text variant="body">
              Total{' '}
              <strong>{this.loading ? '...' : FormatNumber(this.totalCount)} responses</strong>{' '}
              available. Download as:
            </e-text>

            {/* Download as label and dropdown */}
            <div style={{ flex: '0 0 auto', display: 'flex', alignItems: 'center' }}>
              <div style={{ minWidth: '150px' }}>
                <e-select
                  value={this.selectedFormat}
                  options={JSON.stringify(this.formatOptions)}
                  name="exportFormat"
                  onSelectChangeEvent={this.handleFormatChange}
                />
              </div>
            </div>

            {/* Request Download button */}
            <div style={{ flex: '0 0 auto' }}>
              <e-button
                variant="primary"
                disabled={this.loading || this.totalCount === 0 || this.isSubmittingRequest}
                onClick={this.submitExportRequest}
              >
                {this.isSubmittingRequest ? 'Submitting...' : 'Request Download'}
              </e-button>
            </div>
          </l-row>
        </c-card>

        {/* Export Requests Table */}
        <l-spacer value={2}></l-spacer>
        <c-card>
          <e-text variant="subheading">Export Requests</e-text>
          <l-spacer value={1}></l-spacer>

          {this.exportRequests.length === 0 ? (
            <div style={{ textAlign: 'center', padding: '2em', color: 'var(--color__grey--600)' }}>
              <e-text variant="body">There are no export requests</e-text>
            </div>
          ) : (
            <div style={{ overflowX: 'auto' }}>
              <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                <thead>
                  <tr style={{ borderBottom: '2px solid var(--color__grey--200)' }}>
                    <th style={{ textAlign: 'left', padding: '0.75em', fontWeight: 'bold' }}>
                      <e-text variant="body">Name</e-text>
                    </th>
                    <th style={{ textAlign: 'left', padding: '0.75em', fontWeight: 'bold' }}>
                      <e-text variant="body">Date</e-text>
                    </th>
                    <th style={{ textAlign: 'left', padding: '0.75em', fontWeight: 'bold' }}>
                      <e-text variant="body">Status</e-text>
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {this.exportRequests.map(request => (
                    <tr
                      key={request.id}
                      style={{ borderBottom: '1px solid var(--color__grey--100)' }}
                    >
                      <td style={{ padding: '0.75em' }}>
                        <e-text variant="body">
                          <strong>{request.format.toUpperCase()} Export</strong>
                        </e-text>
                      </td>
                      <td style={{ padding: '0.75em' }}>
                        <e-text variant="body">{this.formatRequestDate(request.createdAt)}</e-text>
                      </td>
                      <td style={{ padding: '0.75em' }}>
                        <div
                          style={{
                            display: 'inline-block',
                            padding: '0.25em 0.75em',
                            borderRadius: 'var(--border-radius)',
                            backgroundColor: this.getStatusBadgeColor(request.status),
                            color: 'white',
                            fontSize: '0.75em',
                            fontWeight: 'bold',
                            textTransform: 'uppercase',
                          }}
                        >
                          {request.status}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </c-card>
      </div>
    );
  }
}
