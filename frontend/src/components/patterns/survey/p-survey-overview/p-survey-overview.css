/* Survey overview pattern styles */
:host {
  display: block;
  width: 100%;
  padding-bottom: 6em;
}

/* Skeleton loading animation */
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

c-card {
  width: 100%;
}

/* Graph Container Styles */
#responses-graph {
  position: relative !important;
  overflow: hidden !important;
  z-index: 1 !important;
}

#responses-graph .plotly {
  position: relative !important;
  z-index: 1 !important;
}

#responses-graph .plotly .svg-container {
  position: relative !important;
  z-index: 1 !important;
  overflow: hidden !important;
}

#responses-graph .plotly .svg-container svg {
  position: relative !important;
  z-index: 1 !important;
  max-width: 100% !important;
  max-height: 100% !important;
}

/* Chart bar hover effects */
#responses-graph .bars .point {
  transition: fill 0.2s ease !important;
}
