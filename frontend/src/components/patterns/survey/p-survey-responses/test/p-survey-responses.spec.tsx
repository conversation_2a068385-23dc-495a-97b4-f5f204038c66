import { newSpecPage } from '@stencil/core/testing';
import { PSurveyResponses } from '../p-survey-responses';

describe('p-survey-responses', () => {
  it('renders', async () => {
    const page = await newSpecPage({
      components: [PSurveyResponses],
      html: `<p-survey-responses survey-id="test-survey"></p-survey-responses>`,
    });
    expect(page.root).toBeTruthy();
  });

  it('should handle delete button click event', async () => {
    const page = await newSpecPage({
      components: [PSurveyResponses],
      html: `<p-survey-responses survey-id="test-survey"></p-survey-responses>`,
    });

    const component = page.rootInstance;
    
    // Mock the deleteResponse method
    const deleteResponseSpy = jest.spyOn(component, 'deleteResponse');
    
    // Simulate button click event
    const mockEvent = {
      detail: {
        action: 'deleteResponse',
        value: 'test-response-id'
      },
      stopPropagation: jest.fn()
    };

    component.handleButtonClickEvent(mockEvent);

    expect(deleteResponseSpy).toHaveBeenCalledWith('test-response-id');
    expect(mockEvent.stopPropagation).toHaveBeenCalled();
  });

  it('should open delete modal when deleteResponse is called', async () => {
    const page = await newSpecPage({
      components: [PSurveyResponses],
      html: `<p-survey-responses survey-id="test-survey"></p-survey-responses>`,
    });

    const component = page.rootInstance;
    
    // Initially modal should be closed
    expect(component.showDeleteModal).toBe(false);
    
    // Call deleteResponse
    component.deleteResponse('test-response-id');
    
    // Modal should now be open
    expect(component.showDeleteModal).toBe(true);
    expect(component.responseToDelete).toBe('test-response-id');
    expect(component.deletionReason).toBe('');
  });

  it('should not open modal for invalid response ID', async () => {
    const page = await newSpecPage({
      components: [PSurveyResponses],
      html: `<p-survey-responses survey-id="test-survey"></p-survey-responses>`,
    });

    const component = page.rootInstance;
    
    // Mock alert
    global.alert = jest.fn();
    
    // Call deleteResponse with invalid ID
    component.deleteResponse('undefined');
    
    // Modal should remain closed
    expect(component.showDeleteModal).toBe(false);
    expect(global.alert).toHaveBeenCalledWith('Error: Invalid response ID. Please refresh the page and try again.');
  });
});
