/**
 * Format numbers with locale-specific formatting
 * @param value The number to format
 * @param locale The locale to use for formatting (defaults to user's locale)
 * @returns Formatted number string
 */
export const FormatNumber = (value: number | string, locale?: string): string => {
  // Convert string to number if needed
  const numValue = typeof value === 'string' ? parseFloat(value) : value;
  
  // Return original value if not a valid number
  if (isNaN(numValue)) {
    return String(value);
  }
  
  // Use provided locale or detect user's locale
  const userLocale = locale || navigator.language || 'en-US';
  
  try {
    // Use Intl.NumberFormat for locale-specific formatting
    return new Intl.NumberFormat(userLocale).format(numValue);
  } catch (error) {
    // Fallback to en-US if locale is not supported
    try {
      return new Intl.NumberFormat('en-US').format(numValue);
    } catch (fallbackError) {
      // Final fallback - manual formatting for basic comma separation
      return numValue.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    }
  }
};

/**
 * Format numbers specifically for Indian locale (1,00,000 format)
 * @param value The number to format
 * @returns Formatted number string in Indian format
 */
export const FormatNumberIndian = (value: number | string): string => {
  const numValue = typeof value === 'string' ? parseFloat(value) : value;
  
  if (isNaN(numValue)) {
    return String(value);
  }
  
  try {
    return new Intl.NumberFormat('en-IN').format(numValue);
  } catch (error) {
    // Manual Indian formatting fallback
    const str = numValue.toString();
    const [integer, decimal] = str.split('.');
    
    if (integer.length <= 3) {
      return str;
    }
    
    // Indian number system: last 3 digits, then groups of 2
    const lastThree = integer.slice(-3);
    const remaining = integer.slice(0, -3);
    const formatted = remaining.replace(/\B(?=(\d{2})+(?!\d))/g, ',') + ',' + lastThree;
    
    return decimal ? formatted + '.' + decimal : formatted;
  }
};
