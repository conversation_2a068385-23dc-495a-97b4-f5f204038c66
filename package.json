{"name": "sensefolks_api", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "nodemon", "build": "tsc", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "repository": {"type": "git", "url": "**************-sensefolks:sensefolks/webapp-api__dev.git"}, "author": "<PERSON><PERSON> (Projckt)", "license": "ISC", "dependencies": {"@types/pdfkit": "^0.17.2", "argon2": "^0.30.3", "connect-redis": "^6.1.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "csrf-csrf": "^4.0.2", "dotenv": "^16.0.3", "express": "^4.18.2", "express-rate-limit": "^7.5.0", "express-session": "^1.18.1", "geoip-lite": "^1.4.7", "google-auth-library": "^9.15.1", "helmet": "^8.1.0", "hpp": "^0.2.3", "ioredis": "^5.3.1", "joi": "^17.7.0", "json2csv": "^5.0.7", "jsonwebtoken": "^9.0.2", "pdfkit": "^0.17.1", "pg": "^8.8.0", "pg-hstore": "^2.3.4", "postmark": "^3.0.15", "rate-limit-redis": "^4.2.0", "sequelize": "^6.28.0", "ts-node": "^10.9.1", "typescript": "^4.9.4"}, "devDependencies": {"@types/connect-redis": "^0.0.20", "@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.13", "@types/express": "^4.17.15", "@types/express-session": "^1.17.6", "@types/geoip-lite": "^1.4.1", "@types/helmet": "^0.0.48", "@types/hpp": "^0.2.6", "@types/json2csv": "^5.0.3", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^18.19.119"}}